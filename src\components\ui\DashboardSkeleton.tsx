'use client';

import React, { memo } from 'react';
import { SkeletonCard } from '@/components/ui/SkeletonLoader';

interface DashboardSkeletonProps {
  type?: 'admin' | 'cremation' | 'furparent';
}

export default memo(function DashboardSkeleton({ type = 'admin' }: DashboardSkeletonProps) {

  // Simplified skeleton rendering using optimized components
  const renderAdminOrCremationSkeleton = () => (
    <div className="space-y-8">
      {/* Welcome section */}
      <SkeletonCard
        withHeader={true}
        contentLines={1}
        withFooter={false}
        withShadow={true}
        rounded="lg"
        animate={true}
      />

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <SkeletonCard
            key={i}
            withHeader={false}
            contentLines={2}
            withFooter={false}
            withShadow={true}
            rounded="lg"
            animate={true}
          />
        ))}
      </div>

      {/* Content area */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <SkeletonCard
          withHeader={true}
          contentLines={5}
          withFooter={false}
          withShadow={true}
          rounded="lg"
          animate={true}
          className="h-64"
        />
        <SkeletonCard
          withHeader={true}
          contentLines={5}
          withFooter={false}
          withShadow={true}
          rounded="lg"
          animate={true}
          className="h-64"
        />
      </div>
    </div>
  );

  // Fur parent dashboard skeleton
  const renderFurParentSkeleton = () => (
    <>
      {/* Hero section */}
      <motion.div
        className="mb-8 bg-white rounded-xl shadow-sm overflow-hidden"
        variants={itemAnimation}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
          {/* Left side - Images grid */}
          <motion.div
            variants={itemAnimation}
          >
            <div className="grid grid-cols-2 gap-4 mb-4">
              <motion.div
                className="aspect-square bg-gray-200 rounded-lg"
                variants={pulseAnimation}
                initial="initial"
                animate="animate"
              />
              <motion.div
                className="aspect-square bg-gray-200 rounded-lg"
                variants={pulseAnimation}
                initial="initial"
                animate="animate"
              />
            </div>
            <motion.div
              className="aspect-video bg-gray-200 rounded-lg"
              variants={pulseAnimation}
              initial="initial"
              animate="animate"
            />
          </motion.div>

          {/* Right side - Content */}
          <motion.div
            variants={itemAnimation}
            className="flex flex-col justify-center"
          >
            <motion.div
              className="h-5 bg-gray-200 rounded-md w-1/3 mb-3"
              variants={pulseAnimation}
              initial="initial"
              animate="animate"
            />
            <motion.div
              className="h-8 bg-gray-200 rounded-md w-3/4 mb-4"
              variants={pulseAnimation}
              initial="initial"
              animate="animate"
            />
            <div className="space-y-2 mb-6">
              {[1, 2, 3].map((i) => (
                <motion.div
                  key={i}
                  className="h-4 bg-gray-200 rounded-md w-full"
                  variants={pulseAnimation}
                  initial="initial"
                  animate="animate"
                  style={{ width: `${100 - (i * 5)}%` }}
                />
              ))}
            </div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center">
                  <motion.div
                    className="w-6 h-6 rounded-full bg-gray-200 mr-3"
                    variants={pulseAnimation}
                    initial="initial"
                    animate="animate"
                  />
                  <motion.div
                    className="h-4 bg-gray-200 rounded-md w-1/2"
                    variants={pulseAnimation}
                    initial="initial"
                    animate="animate"
                  />
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Services section */}
      <motion.div
        className="mb-8"
        variants={itemAnimation}
      >
        <motion.div
          className="h-7 bg-gray-200 rounded-md w-1/4 mb-4"
          variants={pulseAnimation}
          initial="initial"
          animate="animate"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <motion.div
              key={i}
              className="bg-white rounded-xl shadow-sm overflow-hidden"
              variants={itemAnimation}
            >
              <motion.div
                className="h-12 bg-gray-200"
                variants={pulseAnimation}
                initial="initial"
                animate="animate"
              />
              <div className="p-4">
                <motion.div
                  className="h-6 bg-gray-200 rounded-md w-3/4 mb-2"
                  variants={pulseAnimation}
                  initial="initial"
                  animate="animate"
                />
                <motion.div
                  className="h-4 bg-gray-200 rounded-md w-1/2 mb-2"
                  variants={pulseAnimation}
                  initial="initial"
                  animate="animate"
                />
                <motion.div
                  className="h-4 bg-gray-200 rounded-md w-1/3 mb-4"
                  variants={pulseAnimation}
                  initial="initial"
                  animate="animate"
                />
                <div className="flex space-x-2">
                  <motion.div
                    className="h-8 bg-gray-200 rounded-md flex-1"
                    variants={pulseAnimation}
                    initial="initial"
                    animate="animate"
                  />
                  <motion.div
                    className="h-8 bg-gray-200 rounded-md flex-1"
                    variants={pulseAnimation}
                    initial="initial"
                    animate="animate"
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </>
  );

  return (
    <motion.div
      className="w-full"
      initial="hidden"
      animate="show"
      variants={containerAnimation}
    >
      {type === 'furparent' ? renderFurParentSkeleton() : renderAdminOrCremationSkeleton()}
    </motion.div>
  );
}
